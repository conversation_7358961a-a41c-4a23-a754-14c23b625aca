# اختبار بسيط للدوال الـ 36
Write-Host "🔥 TESTING 36 COMPREHENSIVE FUNCTIONS" -ForegroundColor Red
Write-Host "====================================" -ForegroundColor Yellow

# فتح ملف HTML
Write-Host "📋 Opening HTML test file..." -ForegroundColor Green
Start-Process "test_comprehensive_functions.html"
Write-Host "✅ HTML test file opened" -ForegroundColor Green

# فحص الملف
$filePath = "assets/modules/bugbounty/BugBountyCore.js"
if (Test-Path $filePath) {
    Write-Host "✅ BugBountyCore.js exists" -ForegroundColor Green
    
    $content = Get-Content $filePath -Raw
    $fileSize = [math]::Round((Get-Item $filePath).Length / 1KB, 2)
    Write-Host "📊 File size: $fileSize KB" -ForegroundColor Cyan
    
    # فحص الدوال الرئيسية
    $functions = @(
        "generateComprehensiveDetailsFromRealData",
        "generateVulnerabilitiesHTML",
        "generateDynamicImpactForAnyVulnerability",
        "generateAdvancedPayloadAnalysis",
        "generateComprehensiveDocumentation",
        "generateComprehensiveVulnerabilityAnalysis",
        "generateDynamicSecurityImpactAnalysis",
        "generateRealTimeVulnerabilityAssessment",
        "generateComprehensiveRiskAnalysis",
        "generateDynamicThreatModelingForVulnerability"
    )
    
    Write-Host ""
    Write-Host "🔍 Checking key comprehensive functions:" -ForegroundColor Cyan
    $found = 0
    foreach ($func in $functions) {
        if ($content -match $func) {
            Write-Host "  ✅ $func" -ForegroundColor Green
            $found++
        } else {
            Write-Host "  ❌ $func" -ForegroundColor Red
        }
    }
    
    Write-Host ""
    Write-Host "📊 RESULTS:" -ForegroundColor Yellow
    Write-Host "Found: $found/$($functions.Count) functions" -ForegroundColor Cyan
    
    if ($found -eq $functions.Count) {
        Write-Host "🎉 ALL KEY FUNCTIONS FOUND!" -ForegroundColor Green
    } elseif ($found -ge 7) {
        Write-Host "⚠️ Most functions found" -ForegroundColor Yellow
    } else {
        Write-Host "❌ Many functions missing" -ForegroundColor Red
    }
    
    # فحص القالب الشامل
    Write-Host ""
    Write-Host "🔍 Template usage check:" -ForegroundColor Cyan
    if ($content -match "comprehensiveTemplate") {
        Write-Host "  ✅ Comprehensive template usage found" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️ Comprehensive template usage not found" -ForegroundColor Yellow
    }
    
    if ($content -match "comprehensive-section") {
        Write-Host "  ✅ Comprehensive CSS sections found" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️ Comprehensive CSS sections not found" -ForegroundColor Yellow
    }
    
} else {
    Write-Host "❌ BugBountyCore.js not found" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎉 Test completed!" -ForegroundColor Green
Write-Host "Use the HTML interface for detailed testing" -ForegroundColor Cyan
